package adhoc.helper

import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import org.springframework.boot.SpringApplication
import org.springframework.context.ConfigurableApplicationContext
import org.testcontainers.containers.DockerComposeContainer
import org.testcontainers.containers.wait.strategy.Wait
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.dynamodb.model.*
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*

class AdhocHelper {

	private static localStackPort

	private static DockerComposeContainer composeContainer
	public static final String LOCAL_STACK_PORT = "4566"
	public static final String LOCALSTACK = "localstack"

	static String getLocalStackPort() {
		return localStackPort
	}

	static {
		startContainer()
	}

	private static void startContainer() {
		composeContainer = new DockerComposeContainer(new File("docker-compose-test.yml"))
				.withExposedService(LOCALSTACK, 4566)
				.waitingFor(LOCALSTACK, Wait.forListeningPort())

		composeContainer.start()
		// Get the actual mapped port from the container
		localStackPort = composeContainer.getServicePort(LOCALSTACK, 4566).toString()
		println("LocalStack started successfully on port: " + localStackPort)
	}

	static void cleanupSpec() {
		//To do cleanup Spec
	}

	static boolean tableExists(DynamoDbClient dynamoDbClient, String tableName) {
		println("Checking if table exists: " + tableName)
		DescribeTableRequest request = DescribeTableRequest.builder()
				.tableName(tableName)
				.build() as DescribeTableRequest
		try {
			dynamoDbClient.describeTable(request)
			println("Table exists: " + tableName)
			return true
		} catch (ResourceNotFoundException ignored) {
			println("Table does not exist: " + tableName)
			return false
		} catch (Exception e) {
			println("Error checking table existence: " + e.getMessage())
			e.printStackTrace()
			return false
		}
	}

	static void createEventsTable(DynamoDbClient dynamoDbClient, String tableName) {
		if (!tableExists(dynamoDbClient, tableName)) {
			CreateTableRequest createTableRequest = CreateTableRequest.builder()
					.tableName(tableName)
					.keySchema(
					KeySchemaElement.builder().attributeName("transactionHash").keyType(KeyType.HASH).build(),
					KeySchemaElement.builder().attributeName("logIndex").keyType(KeyType.RANGE).build()
					)
					.attributeDefinitions(
					AttributeDefinition.builder().attributeName("transactionHash").attributeType(ScalarAttributeType.S).build(),
					AttributeDefinition.builder().attributeName("logIndex").attributeType(ScalarAttributeType.N).build()
					)
					.provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
					.build() as CreateTableRequest

			dynamoDbClient.createTable(createTableRequest)
		}
	}

	static void createBlockHeightTable(DynamoDbClient dynamoDbClient, String tableName) {
		println("Attempting to create table: " + tableName)
		if (!tableExists(dynamoDbClient, tableName)) {
			println("Table does not exist, creating: " + tableName)
			CreateTableRequest createTableRequest = CreateTableRequest.builder()
					.tableName(tableName)
					.keySchema(
					KeySchemaElement.builder().attributeName("id").keyType(KeyType.HASH).build()
					)
					.attributeDefinitions(
					AttributeDefinition.builder().attributeName("id").attributeType(ScalarAttributeType.N).build()
					)
					.provisionedThroughput(ProvisionedThroughput.builder().readCapacityUnits(5).writeCapacityUnits(5).build())
					.build() as CreateTableRequest

			try {
				dynamoDbClient.createTable(createTableRequest)
				println("Table created successfully: " + tableName)
			} catch (Exception e) {
				println("Error creating table: " + e.getMessage())
				e.printStackTrace()
			}
		} else {
			println("Table already exists: " + tableName)
		}
	}

	static boolean bucketExists(S3Client s3Client, String bucketName) {
		println("Checking if bucket exists: " + bucketName)
		try {
			def request = HeadBucketRequest.builder()
					.bucket(bucketName)
					.build() as HeadBucketRequest

			s3Client.headBucket(request)
			println("Bucket exists: " + bucketName)
			return true
		} catch (NoSuchBucketException ignored) {
			println("Bucket does not exist: " + bucketName)
			return false
		} catch (Exception e) {
			println("Error checking bucket existence: " + e.getMessage())
			e.printStackTrace()
			return false
		}
	}

	static void createS3Bucket(S3Client s3Client, String bucketName) {
		println("Attempting to create bucket: " + bucketName)
		if (!bucketExists(s3Client, bucketName)) {
			println("Bucket does not exist, creating: " + bucketName)
			try {
				def request = CreateBucketRequest.builder()
						.bucket(bucketName)
						.build() as CreateBucketRequest
				s3Client.createBucket(request)
				println("Bucket created successfully: " + bucketName)
			} catch (Exception e) {
				println("Error creating bucket: " + e.getMessage())
				e.printStackTrace()
			}
		} else {
			println("Bucket already exists: " + bucketName)
		}
	}

	static Map<String, AttributeValue> getEventItem(DynamoDbClient dynamoDbClient, String tableName, String transactionHash, int logIndex) {
		GetItemRequest request = GetItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"transactionHash", AttributeValue.builder().s(transactionHash).build(),
				"logIndex", AttributeValue.builder().n(String.valueOf(logIndex)).build()
				))
				.build() as GetItemRequest

		try {
			GetItemResponse response = dynamoDbClient.getItem(request)
			return response.item()
		} catch (Exception ignored) {
			return null
		}
	}

	static Map<String, AttributeValue> getBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id) {
		GetItemRequest request = GetItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"id", AttributeValue.builder().n(String.valueOf(id)).build()
				))
				.build() as GetItemRequest

		try {
			GetItemResponse response = dynamoDbClient.getItem(request)
			return response.item()
		} catch (Exception ignored) {
			return null
		}
	}

	static boolean saveEventItem(DynamoDbClient dynamoDbClient, String tableName, Map<String, AttributeValue> item) {
		PutItemRequest request = PutItemRequest.builder()
				.tableName(tableName)
				.item(item)
				.build() as PutItemRequest

		try {
			dynamoDbClient.putItem(request)
			return true
		} catch (Exception ignored) {
			return false
		}
	}

	static boolean saveBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id, long blockNumber) {
		Map<String, AttributeValue> item = new HashMap<>()
		item.put("id", AttributeValue.builder().n(String.valueOf(id)).build())
		item.put("blockNumber", AttributeValue.builder().n(String.valueOf(blockNumber)).build())

		PutItemRequest request = PutItemRequest.builder()
				.tableName(tableName)
				.item(item)
				.build() as PutItemRequest

		try {
			dynamoDbClient.putItem(request)
			return true
		} catch (Exception ignored) {
			return false
		}
	}

	static boolean deleteEventItem(DynamoDbClient dynamoDbClient, String tableName, String transactionHash, int logIndex) {
		DeleteItemRequest request = DeleteItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"transactionHash", AttributeValue.builder().s(transactionHash).build(),
				"logIndex", AttributeValue.builder().n(String.valueOf(logIndex)).build()
				))
				.build() as DeleteItemRequest

		try {
			dynamoDbClient.deleteItem(request)
			return true
		} catch (Exception ignored) {
			return false
		}
	}

	static boolean deleteBlockHeightItem(DynamoDbClient dynamoDbClient, String tableName, long id) {
		DeleteItemRequest request = DeleteItemRequest.builder()
				.tableName(tableName)
				.key(Map.of(
				"id", AttributeValue.builder().n(String.valueOf(id)).build()
				))
				.build() as DeleteItemRequest

		try {
			dynamoDbClient.deleteItem(request)
			return true
		} catch (Exception ignored) {
			return false
		}
	}

	static ConfigurableApplicationContext initApplication() {
		def application = new SpringApplication(BcmonitoringApplication.class)
		return application.run([] as String[])
	}

	static ConfigurableApplicationContext initApplication(Map<String, Object> defaultProperties) {
		def application = new SpringApplication(BcmonitoringApplication.class)
		if (defaultProperties) {
			application.setDefaultProperties(defaultProperties)
		}
		return application.run([] as String[])
	}

	static void closeApplication(ConfigurableApplicationContext applicationContext) {
		if (applicationContext != null) {
			try {
				println("Closing application context")
				applicationContext.close()
				Thread.sleep(1000) // Give time for port to be released
			} catch (Exception e) {
				println("Error closing application context: " + e.message)
			}
		}
	}

	/**
	 * Upload actual ABI files from docker/local/data/s3 directory to test S3 bucket
	 * @param s3Client The S3 client to use for uploading
	 * @param bucketName The target S3 bucket name
	 * @param networkId The network ID (e.g., "3000", "3001")
	 * @param contractNames List of contract names to upload (e.g., ["Token", "Account"])
	 */
	static void uploadRealAbiFiles(S3Client s3Client, String bucketName, String networkId, List<String> contractNames = null) {
		println("Uploading real ABI files for network: ${networkId} to bucket: ${bucketName}")

		def abiDataDir = new File("docker/local/data/s3/${networkId}")
		if (!abiDataDir.exists()) {
			println("Warning: ABI data directory does not exist: ${abiDataDir.absolutePath}")
			return
		}

		def filesToUpload = contractNames ?
				contractNames.collect { "${it}.json" } :
				abiDataDir.listFiles({ file -> file.name.endsWith('.json') } as FileFilter)*.name

		filesToUpload.each { fileName ->
			def abiFile = new File(abiDataDir, fileName.toString())
			if (abiFile.exists()) {
				def key = "${networkId}/${fileName}"
				def content = abiFile.text
				println("Uploading real ABI file: ${key} (${content.length()} bytes)")

				s3Client.putObject(PutObjectRequest.builder()
						.bucket(bucketName)
						.key(key)
						.build() as PutObjectRequest,
						RequestBody.fromString(content))
			} else {
				println("Warning: ABI file not found: ${abiFile.absolutePath}")
			}
		}
	}

	/**
	 * Upload all available real ABI files for a network
	 * @param s3Client The S3 client to use for uploading
	 * @param bucketName The target S3 bucket name
	 * @param networkId The network ID (e.g., "3000", "3001")
	 */
	static void uploadAllRealAbiFiles(S3Client s3Client, String bucketName, String networkId) {
		uploadRealAbiFiles(s3Client, bucketName, networkId, null)
	}
}
