{"address": "0xDc64a140Aa3E981100a9becA4E685f962f0cF6C9", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "discharge", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint16", "name": "toZoneId"}, {"type": "uint256", "name": "amount"}, {"type": "uint64", "name": "timeoutHeight"}, {"type": "bytes32", "name": "traceId"}], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "escrowAccount", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint16", "name": "srcZoneId"}, {"type": "uint16", "name": "dstZoneId"}], "outputs": [{"type": "bytes32", "name": ""}]}, {"type": "function", "name": "getConfig", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "tuple", "name": "config", "components": [{"type": "string", "name": "port"}, {"type": "string", "name": "channel"}, {"type": "string", "name": "version"}]}]}, {"type": "function", "name": "ibc<PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "address", "name": ""}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "ib<PERSON><PERSON><PERSON><PERSON>_"}, {"type": "address", "name": "ibcTokenAddr"}, {"type": "address", "name": "accessCtrlAddr"}], "outputs": []}, {"type": "function", "name": "onAcknowledgementPacket", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "", "components": [{"type": "uint64", "name": "sequence"}, {"type": "string", "name": "source_port"}, {"type": "string", "name": "source_channel"}, {"type": "string", "name": "destination_port"}, {"type": "string", "name": "destination_channel"}, {"type": "bytes", "name": "data"}, {"type": "tuple", "name": "timeout_height", "components": [{"type": "uint64", "name": "revision_number"}, {"type": "uint64", "name": "revision_height"}]}, {"type": "uint64", "name": "timeout_timestamp"}]}, {"type": "bytes", "name": ""}, {"type": "address", "name": ""}], "outputs": []}, {"type": "function", "name": "onChanCloseConfirm", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "", "components": [{"type": "string", "name": "portId"}, {"type": "string", "name": "channelId"}]}], "outputs": []}, {"type": "function", "name": "onChanCloseInit", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "", "components": [{"type": "string", "name": "portId"}, {"type": "string", "name": "channelId"}]}], "outputs": []}, {"type": "function", "name": "onChanOpenAck", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "", "components": [{"type": "string", "name": "portId"}, {"type": "string", "name": "channelId"}, {"type": "string", "name": "counterpartyVersion"}]}], "outputs": []}, {"type": "function", "name": "onChanOpenConfirm", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "", "components": [{"type": "string", "name": "portId"}, {"type": "string", "name": "channelId"}]}], "outputs": []}, {"type": "function", "name": "onChanOpenInit", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "tuple", "name": "msg_", "components": [{"type": "uint8", "name": "order"}, {"type": "string[]", "name": "connectionHops"}, {"type": "string", "name": "portId"}, {"type": "string", "name": "channelId"}, {"type": "tuple", "name": "counterparty", "components": [{"type": "string", "name": "port_id"}, {"type": "string", "name": "channel_id"}]}, {"type": "string", "name": "version"}]}], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "onChanOpenTry", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "tuple", "name": "msg_", "components": [{"type": "uint8", "name": "order"}, {"type": "string[]", "name": "connectionHops"}, {"type": "string", "name": "portId"}, {"type": "string", "name": "channelId"}, {"type": "tuple", "name": "counterparty", "components": [{"type": "string", "name": "port_id"}, {"type": "string", "name": "channel_id"}]}, {"type": "string", "name": "counterpartyVersion"}]}], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "onRecvPacket", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "packet", "components": [{"type": "uint64", "name": "sequence"}, {"type": "string", "name": "source_port"}, {"type": "string", "name": "source_channel"}, {"type": "string", "name": "destination_port"}, {"type": "string", "name": "destination_channel"}, {"type": "bytes", "name": "data"}, {"type": "tuple", "name": "timeout_height", "components": [{"type": "uint64", "name": "revision_number"}, {"type": "uint64", "name": "revision_height"}]}, {"type": "uint64", "name": "timeout_timestamp"}]}, {"type": "address", "name": ""}], "outputs": [{"type": "bytes", "name": "acknowledgement"}]}, {"type": "function", "name": "onTimeoutPacket", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "", "components": [{"type": "uint64", "name": "sequence"}, {"type": "string", "name": "source_port"}, {"type": "string", "name": "source_channel"}, {"type": "string", "name": "destination_port"}, {"type": "string", "name": "destination_channel"}, {"type": "bytes", "name": "data"}, {"type": "tuple", "name": "timeout_height", "components": [{"type": "uint64", "name": "revision_number"}, {"type": "uint64", "name": "revision_height"}]}, {"type": "uint64", "name": "timeout_timestamp"}]}, {"type": "address", "name": "relayer"}], "outputs": []}, {"type": "function", "name": "recoverPacket", "constant": false, "payable": false, "inputs": [{"type": "tuple", "name": "packet", "components": [{"type": "uint64", "name": "sequence"}, {"type": "string", "name": "source_port"}, {"type": "string", "name": "source_channel"}, {"type": "string", "name": "destination_port"}, {"type": "string", "name": "destination_channel"}, {"type": "bytes", "name": "data"}, {"type": "tuple", "name": "timeout_height", "components": [{"type": "uint64", "name": "revision_number"}, {"type": "uint64", "name": "revision_height"}]}, {"type": "uint64", "name": "timeout_timestamp"}]}, {"type": "address", "name": ""}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "registerEscrowAccount", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "srcZoneId"}, {"type": "uint16", "name": "dstZoneId"}, {"type": "bytes32", "name": "eAccount"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "ibcTokenAddr"}, {"type": "address", "name": "accessCtrlAddr"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "setChannel", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "channel"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "tokenTransferSourceChannel", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "tokenTransferSourcePort", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "tokenTransferSourceVersion", "constant": true, "stateMutability": "view", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}, {"type": "function", "name": "transfer", "constant": false, "payable": false, "inputs": [{"type": "bytes32", "name": "accountId"}, {"type": "uint16", "name": "fromZoneId"}, {"type": "uint16", "name": "toZoneId"}, {"type": "uint256", "name": "amount"}, {"type": "uint64", "name": "timeoutHeight"}, {"type": "bytes32", "name": "traceId"}], "outputs": [{"type": "uint256", "name": ""}]}, {"type": "function", "name": "unregisterEscrowAccount", "constant": false, "payable": false, "inputs": [{"type": "uint16", "name": "srcZoneId"}, {"type": "uint16", "name": "dstZoneId"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x9636af0bd58902f748fe2b35b6ae23058d34d0b073ab15320d79087e5786e997", "receipt": {"to": null, "from": "0xf39Fd6e51aad88F6F4ce6aB8827279cffFb92266", "contractAddress": "0xDc64a140Aa3E981100a9becA4E685f962f0cF6C9", "transactionIndex": 0, "gasUsed": "2910896", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xca759d56e89ec088ac36c46fb953ae909fdfbea1224f2982798f8a22793a76e2", "blockNumber": 756, "cumulativeGasUsed": "2910896", "status": 1}}