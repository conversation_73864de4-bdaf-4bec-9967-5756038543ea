{"address": "0x2eBaD9C374940AA64A3013aFfccEa9F007440e19", "abi": [{"type": "event", "anonymous": false, "name": "Initialized", "inputs": [{"type": "uint8", "name": "version", "indexed": false}]}, {"type": "function", "name": "backupAccounts", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "accounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "string", "name": "accountName"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint16[]", "name": "zoneIds"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "reasonCode"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}, {"type": "bytes32", "name": "validatorId"}, {"type": "bool", "name": "accountIdExistence"}, {"type": "address", "name": "accountEoa"}, {"type": "tuple[]", "name": "accountApprovalAll", "components": [{"type": "bytes32", "name": "spanderId"}, {"type": "string", "name": "spenderAccountName"}, {"type": "uint256", "name": "allowanceAmount"}, {"type": "uint256", "name": "approvedAt"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupBusinessZoneAccounts", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "bizAccountsAll", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple[]", "name": "bizAccountsByZoneId", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "tuple", "name": "businessZoneAccountData", "components": [{"type": "string", "name": "accountName"}, {"type": "uint256", "name": "balance"}, {"type": "bytes32", "name": "accountStatus"}, {"type": "uint256", "name": "appliedAt"}, {"type": "uint256", "name": "registeredAt"}, {"type": "uint256", "name": "terminatingAt"}, {"type": "uint256", "name": "terminatedAt"}]}, {"type": "bool", "name": "accountIdExistenceByZoneId"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupFinancialZoneAccounts", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "financialZoneAccounts", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "tuple", "name": "financialZoneAccountData", "components": [{"type": "uint256", "name": "mintLimit"}, {"type": "uint256", "name": "burnLimit"}, {"type": "uint256", "name": "chargeLimit"}, {"type": "uint256", "name": "transferLimit"}, {"type": "uint256", "name": "cumulativeLimit"}, {"type": "uint256", "name": "cumulativeAmount"}, {"type": "uint256", "name": "cumulativeDate"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "issuers", "components": [{"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "string", "name": "name"}, {"type": "uint16", "name": "bankCode"}, {"type": "bool", "name": "issuerIdExistence"}, {"type": "address", "name": "issuerE<PERSON>"}, {"type": "tuple[]", "name": "issuerAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByIssuerId"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupProviders", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "providers", "components": [{"type": "bytes32", "name": "providerId"}, {"type": "tuple", "name": "providerData", "components": [{"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "name"}, {"type": "uint16", "name": "zoneId"}, {"type": "bool", "name": "enabled"}]}, {"type": "address", "name": "providerEoa"}, {"type": "tuple[]", "name": "zoneData", "components": [{"type": "uint16", "name": "zoneId"}, {"type": "string", "name": "zoneName"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupToken", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple", "name": "token", "components": [{"type": "bytes32", "name": "tokenId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "symbol"}, {"type": "uint256", "name": "totalSupply"}, {"type": "bool", "name": "enabled"}]}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "backupValidators", "constant": true, "stateMutability": "view", "payable": false, "inputs": [{"type": "uint256", "name": "offset"}, {"type": "uint256", "name": "limit"}, {"type": "uint256", "name": "deadline"}, {"type": "bytes", "name": "signature"}], "outputs": [{"type": "tuple[]", "name": "validators", "components": [{"type": "bytes32", "name": "validatorId"}, {"type": "bytes32", "name": "name"}, {"type": "bytes32", "name": "issuerId"}, {"type": "bytes32", "name": "role"}, {"type": "bytes32", "name": "validatorAccountId"}, {"type": "bool", "name": "enabled"}, {"type": "bool", "name": "validatorIdExistence"}, {"type": "bool", "name": "issuerIdLinkedFlag"}, {"type": "address", "name": "validatorEoa"}, {"type": "tuple[]", "name": "validAccountExistence", "components": [{"type": "bytes32", "name": "accountId"}, {"type": "bool", "name": "accountIdExistenceByValidatorId"}]}]}, {"type": "uint256", "name": "totalCount"}, {"type": "string", "name": "err"}]}, {"type": "function", "name": "initialize", "constant": false, "payable": false, "inputs": [{"type": "address", "name": "contractManager"}], "outputs": []}, {"type": "function", "name": "version", "constant": true, "stateMutability": "pure", "payable": false, "inputs": [], "outputs": [{"type": "string", "name": ""}]}], "transactionHash": "0x97c602f4ea4e9553adea4c3cc7265c7fcc51ec8cb129caf62f64ef6d5860bec4", "receipt": {"to": null, "from": "0x027CaDD533C40e8017de7ac814baE35f8b08f402", "contractAddress": "0x2eBaD9C374940AA64A3013aFfccEa9F007440e19", "transactionIndex": 0, "gasUsed": "4510679", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "blockHash": "0xc5956e0430755ce6e32fa707da037ffcb43f929ad862408644efb7d08305c64b", "blockNumber": 372, "cumulativeGasUsed": "4510679", "status": 1}}